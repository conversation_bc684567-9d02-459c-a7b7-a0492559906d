#!/usr/bin/env python3
"""
无人机水下体积实时计算 - Standalone模式

此脚本演示了如何以结构化的方式在Isaac Sim中实时计算
一个复杂关节机器人（如无人机）特定部位浸入水中的体积。

- 目标环境: ground_water.usd
- 目标对象: /World/iris (无人机)
- 核心计算: 实时切分碰撞网格，计算水下部分的体积
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

# 🔧 导入阻尼系数自动计算器
from damping_coefficient_calculator import get_damping_config_dict
from stable_damping_system import create_stable_damping_system, StableDampingSystem
from hydrodynamic_damping_system import create_hydrodynamic_damping_system, HydrodynamicDampingSystem

import os
import numpy as np
import carb
import omni.usd
from pxr import UsdGeom
from isaacsim.core.api import World                    # Isaac Sim世界管理器
from isaacsim.core.prims import Articulation,RigidPrim
from isaacsim.core.utils.stage import add_reference_to_stage
from isaacsim.core.utils.prims import get_prim_at_path # 获取场景对象的工具函数
import isaacsim.core.utils.mesh as mesh_utils
import isaacsim.core.utils.stage as stage_utils
from utils_calculate.geometry_utils_volume import slice_convex_poly_volume

class IrisBuoyancySim:
    """
    无人机浮力仿真主类
    """
    def __init__(self):
        """初始化仿真环境状态"""
        self.world = None
        self.iris_robot = None
        self.target_collision_prim = None
        self.coord_prim = None
        # 添加ArticulationPrim，可以获取到世界Pose
        self.robot_Articulation_prim = None

        self.time_flag = 1
        # 记录质心
        self.coms = None

        # 🎯 阻尼参数设置
        self.damping_params = {
            'target_terminal_velocity': 1.5,  # 目标终端速度 (m/s)
            'stability_enhancement': True,    # 是否启用姿态稳定增强
            'use_empirical_tuning': True,     # 使用经验调优（推荐）
        }

        # 记录一下重力值，后续不需要计算的
        self.gravity = None
        # 记录一下当前的小g
        self.gravity_magnitude = 9.81
        self.world_vertices_record = None
        self.gravity_vector = np.array([0.0, 0.0, -9.81])
        # 计算浮力
        self.water_density = 1000.0  # 水的密度 (kg/m^3)
        self.last_volume_data = None # 记录一下上一帧的体积数据
        self.buoyancy_force = None # 记录一下上一帧的浮力

        # 🚀 阻尼系数将在场景设置后计算（使用实际物理属性）
        self.damping_config = None  # 将在 setup_scene 中计算

        # 🌊 阻尼系统选择
        self.stable_damping_system = None       # 稳定阻尼系统
        self.hydrodynamic_damping_system = None # 水动力学阻尼系统
        self.use_stable_damping = False         # 是否使用稳定阻尼系统
        self.use_hydrodynamic_damping = True    # 是否使用水动力学阻尼系统

        # 默认配置参数
        self.config = {
            'water_surface_z': 0,          # 水面高度 (Z轴)
            'water_plane_normal': (0, 0, 1), # 水平面法线
            'robot_prim_path': "/World/iris",
            'collision_body_rel_path': "base_link/collisions", # 要计算体积的碰撞体
            'debug_mode': True,

            # 🎯 自动计算的阻尼系数（基于物体参数和经验调优）
            'fluid_density': self.water_density, # 水的密度 kg/m^3 (海水约1025)

            'record_data_filename': "cog_simulation_data.npz", # 数据记录文件名
        }

        # 🚀 阻尼系数将在场景设置后更新到配置中

        self.data_log = {
            'sim_frame': [],
            'com_position':[],
            'liner_velocity':[],
            'quaternion_orientation':[],
        }

        self.coord_prim_path = "/World/world_point"

        # 调试和性能控制
        self.sim_frame_count = 0

        # 分块可视化标记，逐帧率打印
        self.water_volume_fps = True

        # 数据的记录
    # 完成对于质量，惯性，尺寸的获取
    def _get_actual_physical_params(self):
        """
        从 USD 场景中获取实际的物理属性
        """
        from pxr import UsdPhysics, Gf
        cube_mesh_path = "/World/iris/base_link/collisions"
        cube_mesh_prim = stage_utils.get_current_stage().GetPrimAtPath(cube_mesh_path)

        if cube_mesh_prim.IsValid():
            print(f"✅ 找到 Cube Mesh: {cube_mesh_path}")

            # 获取质量
            if cube_mesh_prim.HasAPI(UsdPhysics.MassAPI):
                mass_api = UsdPhysics.MassAPI(cube_mesh_prim)
                if mass_api.GetMassAttr().HasValue():
                    actual_mass = float(mass_api.GetMassAttr().Get())
                    print(f"📊 实际 Mesh 质量: {actual_mass} kg")

                # 获取惯性
                if mass_api.GetDiagonalInertiaAttr().HasValue():
                    inertia_vec = mass_api.GetDiagonalInertiaAttr().Get()  # Gf.Vec3f
                    actual_inertia = [float(inertia_vec[0]), float(inertia_vec[1]), float(inertia_vec[2])]
                    print(f"📊 实际 Diagonal inertia: {actual_inertia}")
                else:
                    print("⚠️  没有设置惯量属性，使用默认值")
            else:
                print("⚠️  Mesh 没有 Mass API，使用默认质量")

            # 获取缩放
            cube_xformable = UsdGeom.Xformable(cube_mesh_prim)
            xform_ops = cube_xformable.GetOrderedXformOps()

            for op in xform_ops:
                if op.GetOpType() == UsdGeom.XformOp.TypeScale:
                    scale_vec = op.Get()
                    actual_scale = [float(scale_vec[0]), float(scale_vec[1]), float(scale_vec[2])]
                    print(f"📊 实际 Scale 缩放: {actual_scale}")
                    break
        else:
            print(f"❌ 无法找到 Cube Mesh: {cube_mesh_path}，使用默认参数")

        return {
            'mass': actual_mass,
            'diagonal_inertia': actual_inertia,
            'scale': actual_scale
        }
        
    def apply_config(self, config_dict):
        """应用外部配置参数到仿真实例"""
        self.config.update(config_dict)
        print(f"✅ 配置已应用: {len(config_dict)} 个参数")

    def setup_scene(self, environment_usd_path: str, robot_usd_path: str):
        """设置仿真世界、加载环境和机器人"""
        print("🌊 开始设置场景...")

        # 1. 加载环境USD
        if not os.path.exists(environment_usd_path):
            carb.log_error(f"❌ 环境USD文件不存在: {environment_usd_path}")
            return False
        
        omni.usd.get_context().open_stage(environment_usd_path)
        # 更改渲染步长和物理步长
        # 2. 创建并初始化World对象
        self.world = World(
            physics_dt = 1.0 / 120.0,
            rendering_dt = 1.0 / 60.0,
            stage_units_in_meters=1.0)

        self.world.initialize_physics()
        
        # 3. 加载机器人USD
        if not os.path.exists(robot_usd_path):
            carb.log_error(f"❌ 机器人USD文件不存在: {robot_usd_path}")
            return False
        robot_path = self.config['robot_prim_path']
        add_reference_to_stage( 
            usd_path=robot_usd_path, 
            prim_path=robot_path
        )

        # for prim in self.world.stage.Traverse():
        #     print("prim:",prim.GetPath())

        # 4. 创建机器人仿真对象（使用 Articulation）

        self.iris_robot = self.world.scene.add(
            Articulation(
                prim_paths_expr=robot_path,
                name="iris"
            )
        )
        print(f"✅ 刚体机器人 '{robot_path}' 已添加")

        self.robot_Articulation_prim = self.world.scene.get_object("iris")
        print(f"  - 机器人世界坐标: {self.robot_Articulation_prim.get_world_poses()}")
        # 5. 获取用于计算体积的目标碰撞体Prim
        collision_prim_path = f"{robot_path.rstrip('/')}/{self.config['collision_body_rel_path'].lstrip('/')}"
        print("collision_prim_path:",collision_prim_path)
        # collision_prim_path: /World/iris/base_link/collisions

        # 获取原始 Prim
        collision_prim = stage_utils.get_current_stage().GetPrimAtPath(collision_prim_path)

        if not collision_prim.IsValid():
            carb.log_error(f"❌ 无法找到目标碰撞体: {collision_prim_path}")
            return False

        # 检查 prim 类型并转换为 UsdGeom.Mesh，一定需要处理mesh类型，否则无法使用顶点api
        if collision_prim.IsA(UsdGeom.Mesh):
            self.target_collision_prim = UsdGeom.Mesh(collision_prim)
            print(f"✅ 目标碰撞体已锁定为 Mesh: {collision_prim_path}")
        else:
            # 如果不是 Mesh，尝试查找子级中的 Mesh
            print(f"⚠️  Prim {collision_prim_path} 不是 Mesh 类型，尝试查找子级 Mesh...")
            mesh_found = False
            for child in collision_prim.GetAllChildren():
                if child.IsA(UsdGeom.Mesh):
                    self.target_collision_prim = UsdGeom.Mesh(child)
                    print(f"✅ 找到子级 Mesh: {child.GetPath()}")
                    mesh_found = True
                    break

            if not mesh_found:
                carb.log_error(f"❌ 在 {collision_prim_path} 及其子级中未找到有效的 Mesh")
                return False
        

        # 5.1 创建参考点
        self.coord_prim  = stage_utils.get_current_stage().GetPrimAtPath(self.coord_prim_path)
        
        # 主体管理
        self.rigid_link = RigidPrim(prim_paths_expr="/World/iris/base_link", name="iris_body_link")

        # 6. 重置世界，让所有对象初始化
        self.world.reset()

        # 🎯 获取实际物理属性并计算阻尼系数
        actual_params = self._get_actual_physical_params()

        # 🚀 使用实际物理属性计算阻尼系数
        print("🔧 正在根据实际物体参数自动计算阻尼系数...")

        # 根据缩放调整尺寸
        actual_length = actual_params['scale'][0]
        actual_width = actual_params['scale'][1]
        actual_height = actual_params['scale'][2]

        self.damping_config = get_damping_config_dict(
            length=actual_length,
            width=actual_width,
            height=actual_height,
            mass=actual_params['mass'],
            diagonal_inertia=actual_params['diagonal_inertia'],
            target_terminal_velocity=self.damping_params['target_terminal_velocity'],
            stability_enhancement=self.damping_params['stability_enhancement'],
            use_empirical_tuning=self.damping_params['use_empirical_tuning']
        )

        print("✅ 基于实际物理属性的阻尼系数计算完成:")
        print(f"  📊 使用质量: {actual_params['mass']} kg")
        print(f"  📊 使用惯性: {actual_params['diagonal_inertia']}")
        print(f"  📊 使用尺寸: L={actual_length:.2f}, W={actual_width:.2f}, H={actual_height:.2f}")
        print(f"  🔧 线性阻尼: {self.damping_config['linear_drag_coefficient']} N·s/m")
        print(f"  🔧 二次阻尼: {self.damping_config['quadratic_drag_coefficient']} N·s²/m²")
        print(f"  🔧 角线性阻尼: {self.damping_config['angular_drag_coefficient']} N·m·s/rad")
        print(f"  🔧 角二次阻尼: {self.damping_config['angular_quadratic_drag_coefficient']} N·m·s²/rad²")
        print(f"  🔧 稳定化因子: {self.damping_config['stability_factor']}")

        # 🚀 将计算出的阻尼系数添加到配置中
        self.config.update(self.damping_config)

        # 🌊 初始化阻尼系统
        characteristic_length = max(actual_length, actual_width, actual_height)

        if self.use_hydrodynamic_damping:
            print("\n🌊 初始化水动力学阻尼系统...")

            # 创建水动力学阻尼系统
            self.hydrodynamic_damping_system = create_hydrodynamic_damping_system(
                mass=actual_params['mass'],
                inertia=actual_params['diagonal_inertia'],
                characteristic_length=characteristic_length
            )

            print(f"✅ 水动力学阻尼系统已初始化")
            print(f"  📊 特征长度: {characteristic_length:.3f} m")

            # 显示系统状态
            status = self.hydrodynamic_damping_system.get_system_status()
            print(f"  📊 基础线性阻尼: {status['base_linear_damping']:.2f} N·s/m")
            print(f"  📊 基础二次阻尼: {status['base_quadratic_damping']:.2f} N·s²/m²")
            print(f"  📊 基础角阻尼: {status['base_angular_damping']:.2f} N·m·s/rad")
            print(f"  📊 速度分层: {len(status['velocity_zones'])} 个区间")

        elif self.use_stable_damping:
            print("\n🛡️ 初始化稳定阻尼系统...")

            # 创建稳定阻尼系统（启用快速角度响应）
            self.stable_damping_system = create_stable_damping_system(
                mass=actual_params['mass'],
                inertia=actual_params['diagonal_inertia'],
                characteristic_length=characteristic_length,
                fast_angular_response=True  # 🎯 启用快速角度响应
            )

            print(f"✅ 稳定阻尼系统已初始化")
            print(f"  📊 特征长度: {characteristic_length:.3f} m")

            # 显示系统状态
            status = self.stable_damping_system.get_system_status()
            print(f"  📊 临界线性阻尼: {status['critical_linear_damping']:.2f} N·s/m")
            print(f"  📊 稳定线性阻尼: {status['stable_linear_damping']:.2f} N·s/m")
            print(f"  📊 临界角阻尼: {status['critical_angular_damping']:.2f} N·m·s/rad")
            print(f"  📊 稳定角阻尼: {status['stable_angular_damping']:.2f} N·m·s/rad")
        else:
            print("⚠️  使用传统固定阻尼系数")

        # 7. 注册物理回调！
        self.world.add_physics_callback(
            "iris_volume_calculator",
            callback_fn=self._volume_calculation_callback
        )
        print("✅ 物理回调函数 'iris_volume_calculator' 已注册")

        # 8. 计算重力[返回一个元组，第一个元素对应重力方向向量，第二个元素是大小]
        self.gravity_vector, self.gravity_magnitude = self.world.get_physics_context().get_gravity()
        self.gravity_vector = np.array(self.gravity_vector)
        self.gravity = self.gravity_magnitude * sum(sum(self.iris_robot.get_body_masses()))
        print("mass:",sum(sum(self.iris_robot.get_body_masses())))
        print("gravity:",self.gravity)
        print("gravity_vector:",self.gravity_vector)

        # 运行几帧让物理稳定
        for _ in range(5):
            simulation_app.update()

        print("🎉 场景设置成功!")
        return True

    def switch_damping_mode(self, damping_type: str = None):
        """
        动态切换阻尼模式

        Args:
            damping_type: 阻尼系统类型 ('hydrodynamic', 'stable', 'fixed')
        """
        if damping_type:
            if damping_type == 'hydrodynamic':
                self.use_hydrodynamic_damping = True
                self.use_stable_damping = False
                print(f"🔧 阻尼模式切换: 水动力学阻尼系统")
            elif damping_type == 'stable':
                self.use_hydrodynamic_damping = False
                self.use_stable_damping = True
                print(f"🔧 阻尼模式切换: 稳定阻尼系统")
            elif damping_type == 'fixed':
                self.use_hydrodynamic_damping = False
                self.use_stable_damping = False
                print(f"🔧 阻尼模式切换: 传统固定阻尼")

    def get_damping_status(self) -> dict:
        """获取当前阻尼系统状态"""
        status = {
            'use_hydrodynamic_damping': self.use_hydrodynamic_damping,
            'use_stable_damping': self.use_stable_damping,
            'hydrodynamic_system_available': self.hydrodynamic_damping_system is not None,
            'stable_system_available': self.stable_damping_system is not None
        }

        if self.hydrodynamic_damping_system and self.use_hydrodynamic_damping:
            try:
                hydro_status = self.hydrodynamic_damping_system.get_system_status()
                status.update({'hydro_' + k: v for k, v in hydro_status.items()})
            except Exception as e:
                print(f"⚠️  获取水动力学阻尼状态失败: {e}")
                status['hydro_error'] = str(e)

        if self.stable_damping_system and self.use_stable_damping:
            try:
                stable_status = self.stable_damping_system.get_system_status()
                status.update({'stable_' + k: v for k, v in stable_status.items()})
            except Exception as e:
                print(f"⚠️  获取稳定阻尼状态失败: {e}")
                status['stable_error'] = str(e)

        return status

    def _get_mesh_vertices_safe(self):
        """
        安全的获取网格顶点的备用方法，避免 MaterialBindingAPI 问题
        """
        try:
            # 获取本地顶点
            points_attr = self.target_collision_prim.GetPointsAttr()
            if not points_attr:
                raise ValueError("Mesh 没有顶点属性")

            local_points = points_attr.Get()
            if not local_points:
                raise ValueError("Mesh 顶点数据为空")

            # 转换为 numpy 数组
            local_vertices = np.array(local_points, dtype=np.float32)

            # 获取变换矩阵
            from isaacsim.core.utils.transformations import get_relative_transform
            transform_matrix = get_relative_transform(
                self.target_collision_prim.GetPrim(),
                self.coord_prim
            )

            # 应用变换
            # 添加齐次坐标
            homogeneous_vertices = np.hstack([
                local_vertices,
                np.ones((local_vertices.shape[0], 1))
            ])

            # 应用变换矩阵
            transformed_vertices = (transform_matrix @ homogeneous_vertices.T).T

            # 返回前三列（去掉齐次坐标）
            return transformed_vertices[:, :3]

        except Exception as e:
            carb.log_error(f"❌ 备用顶点获取方法失败: {e}")
            return None

    def _volume_calculation_callback(self, step_size):
        """
        物理回调函数。此函数会在每个物理步进后自动执行。
        """
        self.sim_frame_count += 1

        # 按固定频率执行计算和打印
        # if self.sim_frame_count % self.debug_interval == 0:

        if not self.target_collision_prim:
            self.last_volume_data = None
            return

        # 验证 coord_prim 是否有效
        if not self.coord_prim or not self.coord_prim.IsValid():
            carb.log_error("❌ coord_prim 无效")
            self.last_volume_data = None
            return

        try:
            # 验证 mesh prim 是否仍然有效
            if not self.target_collision_prim or not self.target_collision_prim.GetPrim().IsValid():
                carb.log_error("❌ target_collision_prim 无效")
                self.last_volume_data = None
                return

            # 尝试使用 mesh_utils，如果失败则使用备用方法
            try:
                world_vertices = mesh_utils.get_mesh_vertices_relative_to(
                    self.target_collision_prim, self.coord_prim
                )
            except RuntimeError as e:
                if "Accessed schema on invalid prim" in str(e):
                    print("⚠️  mesh_utils 方法失败，使用备用方法获取顶点...")
                    world_vertices = self._get_mesh_vertices_safe()
                else:
                    raise e
            self.world_vertices_record = world_vertices
            # print("world_vertices_in_callbacks:\n",world_vertices)


            # 获取到淹没体积和浮心
            total, below, cb = slice_convex_poly_volume(
                world_vertices,
                plane_normal=self.config['water_plane_normal'],
                plane_d=self.config['water_surface_z']
            )
            
            # TODO 或许添加对于有效体积判断，然后再计算浮心
            self.time_flag += 1
            self.last_volume_data = {
                'total_volume': total,
                'submerged_volume': below,
                'center_buoyancy': cb,
                'time_flag': self.time_flag,
                'world_vertices': world_vertices
            }
            if self.water_volume_fps:
                print("-----------------------------------------------------------------进入回调函数")
                print(f"\n--- [帧 {self.sim_frame_count}] ---")
                print(f"💧 水下体积计算结果:")
                print(f"   - 总 体 积: {self.last_volume_data['total_volume']:.6f} m³")
                print(f"   - 水下体积: {self.last_volume_data['submerged_volume']:.6f} m³")
                print(f"   - 浮心坐标: {self.last_volume_data['center_buoyancy']}")
                # print(f"   - 时间戳: {self.last_volume_data['time_flag']}")
                print(f"   - 世界坐标: {self.last_volume_data['world_vertices']}")

            # 2、计算并应用浮力
            if self.last_volume_data['submerged_volume'] > 1e-6:
                # r v g
                F_buoyancy_magnitude = self.config['fluid_density'] * \
                                       self.last_volume_data['submerged_volume'] * self.gravity_magnitude
                
                buoyancy_direction = np.array([0.0, 0.0, 1.0])  # 浮力默认向上

                buoyancy_force_vector = F_buoyancy_magnitude * buoyancy_direction

                print("浮力向量:",buoyancy_force_vector)
                forces__buoyancy = np.array([buoyancy_force_vector],dtype=np.float32)  # 确保都为1,3向量
                torques_buoyancy = np.zeros((1,3), dtype=np.float32)
                pos_buoyancy = np.array([self.last_volume_data['center_buoyancy']], dtype=np.float32)
                # 将浮力施加到浮心位置
                self.rigid_link.apply_forces_and_torques_at_pos(
                    forces=forces__buoyancy,
                    torques=torques_buoyancy,
                    positions=pos_buoyancy,
                    is_global=True # 持续的力
                )
                print("重力大小:",self.gravity)

                current_linear_velocity = self.rigid_link.get_linear_velocities()
                print("机器人当前线速度:", current_linear_velocity)


            # 3. **计算并应用水下阻尼力与阻尼力矩**
            liner_velocity = self.rigid_link.get_linear_velocities()
            angular_velocity = self.rigid_link.get_angular_velocities()

            self.coms = self.rigid_link.get_world_poses()
            if self.last_volume_data['submerged_volume'] > 1e-9: # 仅在有淹没体积时施加阻尼
                # 计算淹没比例（用于调整阻尼力大小）
                # 物体实际体积: 2m × 1m × 0.5m = 1.0 m³
                total_volume = total  # 物体总体积 (m³) - 与实际体积匹配
                submerged_ratio = min(self.last_volume_data['submerged_volume'] / total_volume, 1.0)

                # 获取速度的大小
                velocity_magnitude = np.linalg.norm(liner_velocity)
                angular_velocity_magnitude = np.linalg.norm(angular_velocity)

                # 🌊 选择阻尼系统计算阻尼力
                if self.use_hydrodynamic_damping and self.hydrodynamic_damping_system:
                    # 🌊 使用水动力学阻尼系统
                    try:
                        F_drag_linear, T_drag_angular = self.hydrodynamic_damping_system.get_damping_forces(
                            linear_velocity=liner_velocity[0],  # 取第一个环境的速度
                            angular_velocity=angular_velocity[0]  # 取第一个环境的角速度
                        )

                        # 应用淹没比例
                        F_drag_linear = F_drag_linear * submerged_ratio
                        T_drag_angular = T_drag_angular * submerged_ratio

                        # 获取调试信息
                        try:
                            damping_result = self.hydrodynamic_damping_system.calculate_hydrodynamic_damping(
                                liner_velocity[0], angular_velocity[0]
                            )
                            debug_info = damping_result['debug_info']

                            print(f"🌊 水动力学阻尼信息:")
                            print(f"  线速度: {debug_info['linear_speed']:.3f} m/s")
                            print(f"  平滑线速度: {debug_info['smooth_linear_speed']:.3f} m/s")
                            print(f"  角速度: {debug_info['angular_speed']:.3f} rad/s")
                            print(f"  速度区间: {debug_info['velocity_zone']}")
                            print(f"  雷诺数: {debug_info['reynolds_number']:.0f}")
                            print(f"  线性主导: {debug_info['linear_dominance']:.2f}")
                            print(f"  二次因子: {debug_info['quadratic_factor']:.2f}")
                            print(f"  阻尼因子: {debug_info['linear_damping_factor']:.3f}")
                        except Exception as debug_e:
                            print(f"⚠️  水动力学调试信息获取失败: {debug_e}")

                    except Exception as e:
                        print(f"❌ 水动力学阻尼计算失败: {e}")
                        print("🔧 回退到传统阻尼计算...")
                        self.use_hydrodynamic_damping = False

                elif self.use_stable_damping and self.stable_damping_system:
                    try:
                        F_drag_linear, T_drag_angular = self.stable_damping_system.get_damping_forces(
                            linear_velocity=liner_velocity[0],  # 取第一个环境的速度
                            angular_velocity=angular_velocity[0]  # 取第一个环境的角速度
                        )

                        # 应用淹没比例
                        F_drag_linear = F_drag_linear * submerged_ratio
                        T_drag_angular = T_drag_angular * submerged_ratio

                        # 获取调试信息
                        try:
                            damping_result = self.stable_damping_system.calculate_stable_damping(
                                liner_velocity[0], angular_velocity[0]
                            )
                            debug_info = damping_result['debug_info']

                            print(f"🛡️ 稳定阻尼信息:")
                            print(f"  线速度: {debug_info['linear_speed']:.3f} m/s")
                            print(f"  平滑线速度: {debug_info['smooth_linear_speed']:.3f} m/s")
                            print(f"  角速度: {debug_info['angular_speed']:.3f} rad/s")
                            print(f"  平滑角速度: {debug_info['smooth_angular_speed']:.3f} rad/s")
                            print(f"  线性调整: {debug_info['linear_adjustment']:.2f}")
                            print(f"  角度调整: {debug_info['angular_adjustment']:.2f}")
                            print(f"  当前线性阻尼: {damping_result['linear_damping']}")
                            print(f"  当前角阻尼: {damping_result['angular_damping']}")
                        except Exception as debug_e:
                            print(f"⚠️  稳定阻尼调试信息获取失败: {debug_e}")

                    except Exception as e:
                        print(f"❌ 稳定阻尼计算失败: {e}")
                        print("🔧 回退到传统阻尼计算...")
                        self.use_stable_damping = False

                else:
                    # 🔧 传统固定阻尼计算（备用）
                    if velocity_magnitude > 1e-6:  # 避免除零错误
                        # 线性阻尼分量
                        linear_drag_component = self.config['linear_drag_coefficient'] * liner_velocity

                        # 二次阻尼分量 (与速度平方成正比，方向与速度相反)
                        velocity_direction = liner_velocity / velocity_magnitude
                        quadratic_drag_component = (self.config['quadratic_drag_coefficient'] *
                                                  velocity_magnitude * velocity_magnitude *
                                                  velocity_direction)

                        # 总阻尼力 = -(线性阻尼 + 二次阻尼) * 淹没比例
                        F_drag_linear = -(linear_drag_component + quadratic_drag_component) * submerged_ratio
                    else:
                        F_drag_linear = np.zeros_like(liner_velocity)

                    # 改进的角阻尼力矩计算
                    if angular_velocity_magnitude > 1e-6:
                        # 线性角阻尼分量
                        linear_angular_drag = self.config['angular_drag_coefficient'] * angular_velocity

                        # 二次角阻尼分量 (与角速度平方成正比，方向与角速度相反)
                        angular_velocity_direction = angular_velocity / angular_velocity_magnitude
                        quadratic_angular_drag = (self.config['angular_quadratic_drag_coefficient'] *
                                                angular_velocity_magnitude * angular_velocity_magnitude *
                                                angular_velocity_direction)

                        # 总角阻尼力矩 = -(线性角阻尼 + 二次角阻尼) * 淹没比例
                        T_drag_angular = -(linear_angular_drag + quadratic_angular_drag) * submerged_ratio

                        # 额外的稳定化阻尼（针对倾转和俯仰）
                        # 对X轴（俯仰）和Y轴（横滚）旋转施加强额外阻尼
                        stability_factor = np.array([3.0, 3.0, 1.0])  # X,Y轴强额外阻尼，Z轴正常
                        T_drag_angular = T_drag_angular * stability_factor
                    else:
                        T_drag_angular = np.zeros_like(angular_velocity)

                print(f"淹没体积: {self.last_volume_data['submerged_volume']:.4f} m³")
                print(f"淹没比例: {submerged_ratio:.2f}")
                print(f"速度大小: {velocity_magnitude:.4f} m/s")
                print("线性速度:",liner_velocity)
                print("阻尼力:",F_drag_linear)
                print("角速度:",angular_velocity)
                print("阻尼力矩:",T_drag_angular)

                # 在全局坐标系中施加力和力矩
                self.rigid_link.apply_forces_and_torques_at_pos(
                    forces=F_drag_linear,      # 施加计算出的线性阻尼力
                    torques=T_drag_angular,    # 施加计算出的角阻尼力矩
                    positions=self.coms[0],
                    is_global=True            # 在全局坐标系中施加力
                )
            
            print("重心:",self.coms[0])
            print("四元数",self.coms[1])
            self.data_log["sim_frame"].append(self.sim_frame_count)
            self.data_log["com_position"].append(self.coms[0])
            self.data_log['liner_velocity'].append(liner_velocity)
            self.data_log['quaternion_orientation'].append(self.coms[1])
            # 1. 获取机器人状态（可选，未来可用于施加力）
            self.base_link_pose = {'position': self.iris_robot.get_world_poses(),
                                   'time_flag': self.time_flag}
            # 计算浮力
            self.buoyancy_force = self.last_volume_data['submerged_volume'] * self.water_density * self.gravity_magnitude
            print("浮力:",self.buoyancy_force)

        except Exception as e:
            carb.log_error(f"❌ 体积计算回调失败: {e}")
            self.last_volume_data = None

    def step(self):
        """执行单步仿真逻辑"""
        # 步进物理仿真和渲染
        self.world.step(render=True)


def main():
    """主函数 - 统一参数配置和仿真流程控制"""

    # ========================================
    # 🎛️ 统一参数配置区域
    # ========================================
    
    # 📁 文件路径配置
    ENV_USD_PATH = "/home/<USER>/Learn_standalone_isaac/usd_assets/ground_water.usd"
    ROBOT_USD_PATH = "/home/<USER>/Learn_standalone_isaac/usd_assets/iris_fov.usd"
    
    # 🌊 水环境参数
    WATER_SURFACE_Z = 0.0  # 水面高度为 Z=0.5 米

    # ========================================
    # 🚀 仿真初始化和运行
    # ========================================

    sim = IrisBuoyancySim()

    # 应用配置
    config_to_apply = {
        'water_surface_z': WATER_SURFACE_Z
    }
    sim.apply_config(config_to_apply)

    # 设置场景
    if not sim.setup_scene(environment_usd_path=ENV_USD_PATH, robot_usd_path=ROBOT_USD_PATH):
        carb.log_error("❌ 场景设置失败，程序退出。")
        simulation_app.close()
        return

    print("\n🎉 仿真设置完成，开始实时计算循环...")

    try:
        while simulation_app.is_running():
            sim.step()
    except KeyboardInterrupt:
        print("\n用户手动中断仿真。")
    except Exception as e:
        carb.log_error(f"❌ 仿真循环中出现严重错误: {e}")
    finally:
        print("关闭仿真程序。")
        print(f"💾 正在保存仿真数据到 '{sim.config['record_data_filename']}'...")
        np.savez(sim.config['record_data_filename'], **sim.data_log)
        print("✅ 数据保存完成。")
        simulation_app.close()

if __name__ == "__main__":
    main()
