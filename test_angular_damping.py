#!/usr/bin/env python3
"""
测试角阻尼效果的脚本
"""

import numpy as np

def calculate_angular_drag_torque(angular_velocity, submerged_ratio=1.0,
                                linear_coeff=300.0, quadratic_coeff=500.0):
    """
    计算角阻尼力矩
    
    Args:
        angular_velocity: 角速度向量 (rad/s) [wx, wy, wz]
        submerged_ratio: 淹没比例
        linear_coeff: 线性角阻尼系数 (N·m·s/rad)
        quadratic_coeff: 二次角阻尼系数 (N·m·s²/rad²)
    
    Returns:
        angular_drag_torque: 角阻尼力矩 (N·m)
    """
    angular_velocity_magnitude = np.linalg.norm(angular_velocity)
    
    if angular_velocity_magnitude > 1e-6:
        # 线性角阻尼分量
        linear_angular_drag = linear_coeff * angular_velocity
        
        # 二次角阻尼分量
        angular_velocity_direction = angular_velocity / angular_velocity_magnitude
        quadratic_angular_drag = (quadratic_coeff * 
                                angular_velocity_magnitude * angular_velocity_magnitude * 
                                angular_velocity_direction)
        
        # 总角阻尼力矩
        total_angular_drag = -(linear_angular_drag + quadratic_angular_drag) * submerged_ratio
        
        # 稳定化阻尼（对俯仰和横滚额外阻尼）
        stability_factor = np.array([2.0, 2.0, 1.0])  # X,Y轴额外阻尼，Z轴正常
        total_angular_drag = total_angular_drag * stability_factor
    else:
        total_angular_drag = np.zeros_like(angular_velocity)
    
    return total_angular_drag, angular_velocity_magnitude

def analyze_angular_damping():
    """分析角阻尼在不同角速度下的效果"""
    print("=== 角阻尼分析 ===")
    print("物体惯性矩: Ixx=104.17, Iyy=354.17, Izz=416.67 kg·m²")
    print("角阻尼系数: 线性=300, 二次=500")
    print()
    
    # 测试不同的角速度
    test_angular_velocities = [
        ([0.1, 0.0, 0.0], "俯仰 0.1 rad/s"),
        ([0.0, 0.1, 0.0], "横滚 0.1 rad/s"), 
        ([0.0, 0.0, 0.1], "偏航 0.1 rad/s"),
        ([0.5, 0.0, 0.0], "俯仰 0.5 rad/s"),
        ([0.0, 0.5, 0.0], "横滚 0.5 rad/s"),
        ([0.0, 0.0, 0.5], "偏航 0.5 rad/s"),
        ([1.0, 0.0, 0.0], "俯仰 1.0 rad/s"),
        ([0.0, 1.0, 0.0], "横滚 1.0 rad/s"),
        ([0.0, 0.0, 1.0], "偏航 1.0 rad/s"),
        ([0.2, 0.3, 0.1], "复合旋转"),
    ]
    
    print("运动类型        | 角速度(rad/s) | 阻尼力矩(N·m) | 力矩大小(N·m)")
    print("-" * 65)
    
    for angular_vel, description in test_angular_velocities:
        angular_vel_array = np.array(angular_vel)
        torque, magnitude = calculate_angular_drag_torque(angular_vel_array)
        torque_magnitude = np.linalg.norm(torque)
        
        print(f"{description:15s} | {angular_vel} | {torque} | {torque_magnitude:11.2f}")

def compare_damping_settings():
    """比较不同角阻尼设置的效果"""
    print("\n=== 不同角阻尼设置比较 ===")
    
    damping_settings = [
        ("当前设置", 300.0, 500.0),
        ("保守设置", 200.0, 300.0),
        ("激进设置", 500.0, 800.0),
        ("超强设置", 800.0, 1200.0),
    ]
    
    test_angular_velocity = np.array([0.5, 0.0, 0.0])  # 俯仰0.5 rad/s
    
    print("设置名称   | 线性系数 | 二次系数 | 阻尼力矩(N·m) | 力矩大小(N·m)")
    print("-" * 70)
    
    for name, linear_coeff, quad_coeff in damping_settings:
        torque, _ = calculate_angular_drag_torque(test_angular_velocity, 
                                                linear_coeff=linear_coeff,
                                                quadratic_coeff=quad_coeff)
        torque_magnitude = np.linalg.norm(torque)
        
        print(f"{name:10s} | {linear_coeff:8.0f} | {quad_coeff:8.0f} | {torque} | {torque_magnitude:11.2f}")

def estimate_angular_settling_time():
    """估算角运动的稳定时间"""
    print("\n=== 角运动稳定时间估算 ===")
    
    # 物体的惯性矩
    inertia_moments = np.array([104.17, 354.17, 416.67])  # kg·m²
    
    # 角阻尼系数
    linear_coeff = 300.0
    quadratic_coeff = 500.0
    stability_factor = np.array([2.0, 2.0, 1.0])
    
    # 有效角阻尼系数（考虑稳定化因子）
    effective_linear_coeff = linear_coeff * stability_factor
    
    print("轴向   | 惯性矩(kg·m²) | 有效线性阻尼 | 阻尼比 | 估算稳定时间(s)")
    print("-" * 70)
    
    axes = ['X(俯仰)', 'Y(横滚)', 'Z(偏航)']
    
    for i, axis in enumerate(axes):
        I = inertia_moments[i]
        c_eff = effective_linear_coeff[i]
        
        # 临界阻尼系数
        c_critical = 2 * np.sqrt(I * 1.0)  # 假设"弹簧常数"为1
        
        # 阻尼比
        damping_ratio = c_eff / c_critical
        
        # 估算稳定时间（95%稳定）
        if damping_ratio > 0:
            settling_time = 3.0 / (damping_ratio * np.sqrt(1.0))  # 简化估算
        else:
            settling_time = float('inf')
        
        print(f"{axis:7s} | {I:12.2f} | {c_eff:11.0f} | {damping_ratio:6.2f} | {settling_time:13.2f}")

def recommend_angular_coefficients():
    """推荐角阻尼系数"""
    print("\n=== 角阻尼系数推荐 ===")
    
    print("基于你的物体参数和需求:")
    print("- 如果倾转角、俯仰角减速太慢，建议:")
    print()
    print("1. 保守增强 (轻微增加阻尼):")
    print("   'angular_drag_coefficient': 400.0")
    print("   'angular_quadratic_drag_coefficient': 600.0")
    print()
    print("2. 中等增强 (当前设置):")
    print("   'angular_drag_coefficient': 300.0")
    print("   'angular_quadratic_drag_coefficient': 500.0")
    print("   + 稳定化因子: X,Y轴 × 2.0")
    print()
    print("3. 激进增强 (快速稳定):")
    print("   'angular_drag_coefficient': 600.0")
    print("   'angular_quadratic_drag_coefficient': 1000.0")
    print("   + 稳定化因子: X,Y轴 × 3.0")
    print()
    print("4. 超强增强 (极快稳定，可能过阻尼):")
    print("   'angular_drag_coefficient': 1000.0")
    print("   'angular_quadratic_drag_coefficient': 1500.0")
    print("   + 稳定化因子: X,Y轴 × 4.0")

if __name__ == "__main__":
    analyze_angular_damping()
    compare_damping_settings()
    estimate_angular_settling_time()
    recommend_angular_coefficients()