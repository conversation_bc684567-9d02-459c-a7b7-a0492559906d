#!/usr/bin/env python3
"""
专门用于显示COM Z和Linear Velocity Z交互式图表的脚本
"""

import numpy as np
import matplotlib
# 设置matplotlib后端，确保图形窗口能正常显示
matplotlib.use('TkAgg')  # 或者 'Qt5Agg'
import matplotlib.pyplot as plt
from matplotlib.widgets import Cursor
import matplotlib.patches as patches

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def find_zero_crossings(data, time_axis):
    """找到数据与0轴的交点"""
    crossings = []
    for i in range(len(data) - 1):
        if (data[i] >= 0 and data[i+1] <= 0) or (data[i] <= 0 and data[i+1] >= 0):
            if data[i+1] != data[i]:
                t_cross = time_axis[i] + (time_axis[i+1] - time_axis[i]) * (-data[i]) / (data[i+1] - data[i])
                crossings.append(t_cross)
    return crossings

def find_extrema(data, time_axis):
    """找到数据的极值点"""
    extrema = []
    for i in range(1, len(data) - 1):
        if data[i] > data[i-1] and data[i] > data[i+1]:
            extrema.append((time_axis[i], data[i], 'max'))
        elif data[i] < data[i-1] and data[i] < data[i+1]:
            extrema.append((time_axis[i], data[i], 'min'))
    return extrema

def create_interactive_plot(time_axis, data, title, ylabel, color='blue'):
    """创建交互式图表"""
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # 绘制主要数据线
    line, = ax.plot(time_axis, data, color=color, linewidth=2, label=title)
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 找到零点交叉
    zero_crossings = find_zero_crossings(data, time_axis)
    
    # 找到极值点
    extrema = find_extrema(data, time_axis)
    
    # 标记零点交叉
    if zero_crossings:
        for i, crossing in enumerate(zero_crossings):
            ax.axvline(x=crossing, color='red', linestyle='--', alpha=0.7)
            ax.plot(crossing, 0, 'ro', markersize=8, label='Zero Crossing' if i == 0 else "")
    
    # 标记极值点
    max_points = [(t, v) for t, v, type_ in extrema if type_ == 'max']
    min_points = [(t, v) for t, v, type_ in extrema if type_ == 'min']
    
    if max_points:
        max_times, max_values = zip(*max_points)
        ax.plot(max_times, max_values, 'g^', markersize=10, label='Local Max')

    if min_points:
        min_times, min_values = zip(*min_points)
        ax.plot(min_times, min_values, 'rv', markersize=10, label='Local Min')
    
    # 添加十字光标
    cursor = Cursor(ax, useblit=True, color='black', linewidth=1)

    # 添加鼠标移动事件处理
    coord_text = ax.text(0.02, 0.98, '', transform=ax.transAxes, fontsize=12,
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # 存储点击的点
    clicked_points = []

    def on_mouse_move(event):
        if event.inaxes == ax:
            coord_text.set_text(f'Hover: ({event.xdata:.2f}, {event.ydata:.6f})')
            fig.canvas.draw_idle()

    def on_click(event):
        if event.inaxes == ax and event.button == 1:  # 左键点击
            # 找到最接近点击位置的数据点
            click_x = event.xdata
            click_y = event.ydata

            # 计算到所有数据点的距离
            distances = np.sqrt((time_axis - click_x)**2 + (data - click_y)**2)
            closest_idx = np.argmin(distances)

            # 获取最接近的点的坐标
            closest_x = time_axis[closest_idx]
            closest_y = data[closest_idx]

            # 移除之前点击的点（如果存在）
            for point in clicked_points:
                point.remove()
            clicked_points.clear()

            # 添加新的点击标记
            clicked_point = ax.plot(closest_x, closest_y, 'yo', markersize=12,
                                  markeredgecolor='black', markeredgewidth=2,
                                  label='Clicked Point')[0]
            clicked_points.append(clicked_point)

            # 添加坐标标注
            annotation = ax.annotate(f'Clicked: ({closest_x:.2f}, {closest_y:.6f})',
                                   xy=(closest_x, closest_y),
                                   xytext=(20, 20),
                                   textcoords='offset points',
                                   fontsize=11,
                                   bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.8),
                                   arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            clicked_points.append(annotation)

            # 更新图例
            ax.legend()
            fig.canvas.draw_idle()

            print(f"📍 点击的点: Frame={closest_x:.2f}, Value={closest_y:.6f}")

    fig.canvas.mpl_connect('motion_notify_event', on_mouse_move)
    fig.canvas.mpl_connect('button_press_event', on_click)
    
    # 设置标签和标题
    ax.set_xlabel('Simulation Frame', fontsize=12)
    ax.set_ylabel(ylabel, fontsize=12)
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.legend(fontsize=10)
    
    # 添加统计信息
    stats_text = f'Statistics:\nMax: {np.max(data):.6f}\nMin: {np.min(data):.6f}\nMean: {np.mean(data):.6f}\nStd: {np.std(data):.6f}\nRange: {np.max(data) - np.min(data):.6f}'
    ax.text(0.98, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', horizontalalignment='right',
            bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 添加操作说明
    help_text = 'Interactive Features:\n• Mouse over for coordinates\n• LEFT CLICK on curve to mark point\n• Scroll to zoom\n• Click and drag to pan\n• Red dots: Zero crossings\n• Green triangles: Local maxima\n• Red triangles: Local minima\n• Yellow dot: Clicked point'
    ax.text(0.02, 0.02, help_text, transform=ax.transAxes, fontsize=9,
            verticalalignment='bottom', horizontalalignment='left',
            bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    plt.tight_layout()
    return fig, ax

def create_combined_interactive_plot(time_axis, com_z_data, vel_z_data=None):
    """创建简化的组合交互式图表，两条曲线在同一个XY轴上"""

    # 创建单个图形
    fig, ax = plt.subplots(1, 1, figsize=(14, 8))

    # 绘制COM Z曲线
    line1, = ax.plot(time_axis, com_z_data, color='blue', linewidth=2, label='COM Z Position')

    # 如果有速度数据，绘制在同一个图上（使用相同的Y轴）
    if vel_z_data is not None:
        line2, = ax.plot(time_axis, vel_z_data, color='red', linewidth=2, label='Linear Velocity Z')

    # 添加网格
    ax.grid(True, alpha=0.3)

    # 存储点击的点
    clicked_points = []

    def on_click(event):
        if event.inaxes == ax and event.button == 1:
            click_x = event.xdata
            click_y = event.ydata

            # 计算到COM Z曲线的距离
            distances_com = np.sqrt((time_axis - click_x)**2 + (com_z_data - click_y)**2)
            closest_idx_com = np.argmin(distances_com)
            closest_dist_com = distances_com[closest_idx_com]

            # 如果有速度数据，也计算到速度曲线的距离
            if vel_z_data is not None:
                distances_vel = np.sqrt((time_axis - click_x)**2 + (vel_z_data - click_y)**2)
                closest_idx_vel = np.argmin(distances_vel)
                closest_dist_vel = distances_vel[closest_idx_vel]

                # 选择距离更近的曲线
                if closest_dist_com <= closest_dist_vel:
                    closest_idx = closest_idx_com
                    closest_x = time_axis[closest_idx]
                    closest_y = com_z_data[closest_idx]
                    curve_name = "COM Z"
                else:
                    closest_idx = closest_idx_vel
                    closest_x = time_axis[closest_idx]
                    closest_y = vel_z_data[closest_idx]
                    curve_name = "Velocity Z"
            else:
                closest_idx = closest_idx_com
                closest_x = time_axis[closest_idx]
                closest_y = com_z_data[closest_idx]
                curve_name = "COM Z"

            # 移除之前的点击标记
            for point in clicked_points:
                point.remove()
            clicked_points.clear()

            # 添加新的点击标记
            clicked_point = ax.plot(closest_x, closest_y, 'yo', markersize=10,
                                  markeredgecolor='black', markeredgewidth=2)[0]
            clicked_points.append(clicked_point)

            # 添加坐标标注
            annotation = ax.annotate(f'{curve_name}: ({closest_x:.2f}, {closest_y:.6f})',
                                   xy=(closest_x, closest_y),
                                   xytext=(20, 20),
                                   textcoords='offset points',
                                   fontsize=11,
                                   bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.8),
                                   arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            clicked_points.append(annotation)

            fig.canvas.draw_idle()
            print(f"📍 {curve_name} 点击的点: Frame={closest_x:.2f}, Value={closest_y:.6f}")

    # 连接点击事件
    fig.canvas.mpl_connect('button_press_event', on_click)

    # 设置标签和标题
    ax.set_xlabel('Simulation Frame', fontsize=12)
    ax.set_ylabel('Z Position (m)', fontsize=12, color='blue')
    ax.set_title('COM Z Position & Linear Velocity Z - Click to Show Coordinates', fontsize=14, fontweight='bold')
    ax.tick_params(axis='y', labelcolor='blue')

    # 创建图例
    lines = [line1]
    labels = ['COM Z Position']
    if vel_z_data is not None:
        lines.append(line2)
        labels.append('Linear Velocity Z')

    ax.legend(lines, labels, loc='upper left')

    plt.tight_layout()
    return fig

def main():
    """主函数"""
    DATA_FILE = "cog_simulation_data.npz"
    
    try:
        # 加载数据
        data_loaded = np.load(DATA_FILE)
        print(f"✅ 成功加载数据文件: {DATA_FILE}")
        
        sim_frames = data_loaded['sim_frame']
        com_positions = data_loaded['com_position']
        
        # 处理COM数据维度
        if len(com_positions.shape) == 3 and com_positions.shape[1] == 1:
            com_positions = com_positions.squeeze(axis=1)
        
        com_z = com_positions[:, 2]
        time_axis = sim_frames
        
        # 检查是否有速度数据
        has_velocity = 'liner_velocity' in data_loaded
        if has_velocity:
            linear_velocities = data_loaded['liner_velocity']
            if len(linear_velocities.shape) == 3 and linear_velocities.shape[1] == 1:
                linear_velocities = linear_velocities.squeeze(axis=1)
            vel_z = linear_velocities[:, 2]

        # 创建组合的交互式图表
        print("🎯 创建COM Z和Linear Velocity Z组合交互式图表...")
        fig = create_combined_interactive_plot(time_axis, com_z, vel_z if has_velocity else None)
        
        print("\n✅ 交互式图表已创建！")
        print("📌 使用说明：")
        print("   • 鼠标悬停显示坐标")
        print("   • 🖱️ 左键点击曲线标记点并显示坐标")
        print("   • 滚轮缩放")
        print("   • 点击拖拽平移")
        print("   • 红点：与0轴交点")
        print("   • 绿色三角：局部最大值")
        print("   • 红色三角：局部最小值")
        print("   • 黄点：你点击的点")
        print("\n🔍 关闭图表窗口以退出程序")
        print("💡 如果窗口没有显示，请检查是否有图形界面支持")

        # 确保图形窗口保持打开状态
        try:
            plt.show(block=True)
        except Exception as e:
            print(f"⚠️ 显示图形时出现问题: {e}")
            print("💡 尝试保存图片到文件...")
            if 'fig_com_z' in locals():
                fig_com_z.savefig('com_z_interactive.png', dpi=300, bbox_inches='tight')
                print("✅ COM Z图表已保存为 'com_z_interactive.png'")
            if 'fig_vel_z' in locals():
                fig_vel_z.savefig('velocity_z_interactive.png', dpi=300, bbox_inches='tight')
                print("✅ Velocity Z图表已保存为 'velocity_z_interactive.png'")
        
    except FileNotFoundError:
        print(f"❌ 错误：未找到数据文件 '{DATA_FILE}'")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
