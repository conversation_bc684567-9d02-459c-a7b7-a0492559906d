#!/usr/bin/env python3
"""
专门用于显示COM Z和Linear Velocity Z交互式图表的脚本
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Cursor
import matplotlib.patches as patches

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def find_zero_crossings(data, time_axis):
    """找到数据与0轴的交点"""
    crossings = []
    for i in range(len(data) - 1):
        if (data[i] >= 0 and data[i+1] <= 0) or (data[i] <= 0 and data[i+1] >= 0):
            if data[i+1] != data[i]:
                t_cross = time_axis[i] + (time_axis[i+1] - time_axis[i]) * (-data[i]) / (data[i+1] - data[i])
                crossings.append(t_cross)
    return crossings

def find_extrema(data, time_axis):
    """找到数据的极值点"""
    extrema = []
    for i in range(1, len(data) - 1):
        if data[i] > data[i-1] and data[i] > data[i+1]:
            extrema.append((time_axis[i], data[i], 'max'))
        elif data[i] < data[i-1] and data[i] < data[i+1]:
            extrema.append((time_axis[i], data[i], 'min'))
    return extrema

def create_interactive_plot(time_axis, data, title, ylabel, color='blue'):
    """创建交互式图表"""
    # 启用交互式模式
    plt.ion()
    
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # 绘制主要数据线
    line, = ax.plot(time_axis, data, color=color, linewidth=2, label=title)
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 找到零点交叉
    zero_crossings = find_zero_crossings(data, time_axis)
    
    # 找到极值点
    extrema = find_extrema(data, time_axis)
    
    # 标记零点交叉
    if zero_crossings:
        for i, crossing in enumerate(zero_crossings):
            ax.axvline(x=crossing, color='red', linestyle='--', alpha=0.7)
            ax.plot(crossing, 0, 'ro', markersize=8, label='Zero Crossing' if i == 0 else "")
            ax.annotate(f'Zero: ({crossing:.2f}, 0.00)', 
                       xy=(crossing, 0), xytext=(10, 10), 
                       textcoords='offset points', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7))
    
    # 标记极值点
    max_points = [(t, v) for t, v, type_ in extrema if type_ == 'max']
    min_points = [(t, v) for t, v, type_ in extrema if type_ == 'min']
    
    if max_points:
        max_times, max_values = zip(*max_points)
        ax.plot(max_times, max_values, 'g^', markersize=10, label='Local Max')
        for t, v in max_points:
            ax.annotate(f'Max: ({t:.2f}, {v:.4f})', 
                       xy=(t, v), xytext=(10, 10), 
                       textcoords='offset points', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.7))
    
    if min_points:
        min_times, min_values = zip(*min_points)
        ax.plot(min_times, min_values, 'rv', markersize=10, label='Local Min')
        for t, v in min_points:
            ax.annotate(f'Min: ({t:.2f}, {v:.4f})', 
                       xy=(t, v), xytext=(10, -20), 
                       textcoords='offset points', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='orange', alpha=0.7))
    
    # 添加十字光标
    cursor = Cursor(ax, useblit=True, color='black', linewidth=1)
    
    # 添加鼠标移动事件处理
    coord_text = ax.text(0.02, 0.98, '', transform=ax.transAxes, fontsize=12,
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    def on_mouse_move(event):
        if event.inaxes == ax:
            coord_text.set_text(f'Coord: ({event.xdata:.2f}, {event.ydata:.6f})')
            fig.canvas.draw_idle()
    
    fig.canvas.mpl_connect('motion_notify_event', on_mouse_move)
    
    # 设置标签和标题
    ax.set_xlabel('Simulation Frame', fontsize=12)
    ax.set_ylabel(ylabel, fontsize=12)
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.legend(fontsize=10)
    
    # 添加统计信息
    stats_text = f'Statistics:\nMax: {np.max(data):.6f}\nMin: {np.min(data):.6f}\nMean: {np.mean(data):.6f}\nStd: {np.std(data):.6f}\nRange: {np.max(data) - np.min(data):.6f}'
    ax.text(0.98, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', horizontalalignment='right',
            bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 添加操作说明
    help_text = 'Interactive Features:\n• Mouse over for coordinates\n• Scroll to zoom\n• Click and drag to pan\n• Red dots: Zero crossings\n• Green triangles: Local maxima\n• Red triangles: Local minima'
    ax.text(0.02, 0.02, help_text, transform=ax.transAxes, fontsize=9,
            verticalalignment='bottom', horizontalalignment='left',
            bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    plt.tight_layout()
    return fig, ax

def main():
    """主函数"""
    DATA_FILE = "cog_simulation_data.npz"
    
    try:
        # 加载数据
        data_loaded = np.load(DATA_FILE)
        print(f"✅ 成功加载数据文件: {DATA_FILE}")
        
        sim_frames = data_loaded['sim_frame']
        com_positions = data_loaded['com_position']
        
        # 处理COM数据维度
        if len(com_positions.shape) == 3 and com_positions.shape[1] == 1:
            com_positions = com_positions.squeeze(axis=1)
        
        com_z = com_positions[:, 2]
        time_axis = sim_frames
        
        # 创建COM Z交互式图表
        print("🎯 创建COM Z交互式图表...")
        fig_com_z, ax_com_z = create_interactive_plot(
            time_axis, com_z, 
            'COM Z Position - Interactive View', 
            'Z Position (m)', 
            'blue'
        )
        
        # 检查是否有速度数据
        if 'liner_velocity' in data_loaded:
            linear_velocities = data_loaded['liner_velocity']
            if len(linear_velocities.shape) == 3 and linear_velocities.shape[1] == 1:
                linear_velocities = linear_velocities.squeeze(axis=1)
            
            vel_z = linear_velocities[:, 2]
            
            # 创建Linear Velocity Z交互式图表
            print("🎯 创建Linear Velocity Z交互式图表...")
            fig_vel_z, ax_vel_z = create_interactive_plot(
                time_axis, vel_z, 
                'Linear Velocity Z - Interactive View', 
                'Z Velocity (m/s)', 
                'red'
            )
        
        print("\n✅ 交互式图表已创建！")
        print("📌 使用说明：")
        print("   • 鼠标悬停显示坐标")
        print("   • 滚轮缩放")
        print("   • 点击拖拽平移")
        print("   • 红点：与0轴交点")
        print("   • 绿色三角：局部最大值")
        print("   • 红色三角：局部最小值")
        print("\n🔍 关闭图表窗口以退出程序")
        
        plt.show()
        
    except FileNotFoundError:
        print(f"❌ 错误：未找到数据文件 '{DATA_FILE}'")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
